from flask import Flask, render_template, request, redirect
import sqlite3

app = Flask(__name__)

# Veritabanı ba<PERSON>langı<PERSON>ı
def init_db():
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("""CREATE TABLE IF NOT EXISTS urunler (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        isim TEXT NOT NULL,
                        aciklama TEXT,
                        fiyat REAL
                    )""")
        conn.commit()

@app.route("/")
def index():
    return render_template("index.html")

@app.route("/hakkimizda")
def hakkimizda():
    return render_template("hakkimizda.html")

@app.route("/hizmetler")
def hizmetler():
    return render_template("hizmetler.html")

@app.route("/urunler")
def urunler():
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("SELECT * FROM urunler")
        urunler = c.fetchall()
    return render_template("urunler.html", urunler=urunler)

@app.route("/iletisim", methods=["GET", "POST"])
def iletisim():
    if request.method == "POST":
        # Buraya e-posta gönderme veya kayıt eklenebilir
        return redirect("/")
    return render_template("iletisim.html")

@app.route("/admin", methods=["GET", "POST"])
def admin():
    if request.method == "POST":
        isim = request.form["isim"]
        aciklama = request.form["aciklama"]
        fiyat = request.form["fiyat"]
        with sqlite3.connect("database.db") as conn:
            c = conn.cursor()
            c.execute("INSERT INTO urunler (isim, aciklama, fiyat) VALUES (?, ?, ?)",
                      (isim, aciklama, fiyat))
            conn.commit()
        return redirect("/admin")
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("SELECT * FROM urunler")
        urunler = c.fetchall()
    return render_template("admin.html", urunler=urunler)

if __name__ == "__main__":
    init_db()
    app.run(debug=True)
