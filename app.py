import os
os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'

from flask_dance.contrib.google import make_google_blueprint, google
from flask import Flask, redirect, url_for, session, render_template, request, g
from flask_bcrypt import Bcrypt
import sqlite3
from werkzeug.utils import secure_filename
from flask_mail import Mail, Message
from itsdangerous import URLSafeTimedSerializer
from dotenv import load_dotenv

# .env dosyasını yükle
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv("SECRET_KEY")
app.config["SESSION_COOKIE_NAME"] = "senkal_session"

# Mail yapılandırması
app.config.update(
    MAIL_SERVER='smtp.gmail.com',
    MAIL_PORT=587,
    MAIL_USE_TLS=True,
    MAIL_USERNAME=os.getenv("MAIL_USERNAME"),
    MAIL_PASSWORD=os.getenv("MAIL_PASSWORD"),
    MAIL_DEFAULT_SENDER=os.getenv("MAIL_DEFAULT_SENDER")
)

mail = Mail(app)
s = URLSafeTimedSerializer(app.secret_key)
app.config['OAUTHLIB_INSECURE_TRANSPORT'] = True  # Sadece development için (HTTP)
bcrypt = Bcrypt(app)

google_bp = make_google_blueprint(
    client_id=os.getenv("GOOGLE_CLIENT_ID"),
    client_secret=os.getenv("GOOGLE_CLIENT_SECRET"),
    scope=["profile", "email"],
    redirect_url="/login/google/authorized"
)
app.register_blueprint(google_bp, url_prefix="/login")

UPLOAD_FOLDER = "static/uploads"
ALLOWED_EXTENSIONS = {"png", "jpg", "jpeg", "gif"}
app.config["UPLOAD_FOLDER"] = UPLOAD_FOLDER

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Veritabanı başlangıcı
def init_db():
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("""
            CREATE TABLE IF NOT EXISTS urunler (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                isim TEXT NOT NULL,
                aciklama TEXT,
                fiyat REAL,
                resim_url TEXT,
                kategori TEXT
            )
        """)
        c.execute("""
            CREATE TABLE IF NOT EXISTS kategoriler (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                isim TEXT NOT NULL UNIQUE
            )
        """)
        c.execute("""
            CREATE TABLE IF NOT EXISTS kullanicilar (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                kullaniciadi TEXT NOT NULL UNIQUE,
                sifre TEXT NOT NULL
            )
        """)

        # Mevcut tabloya kategori sütunu ekle (eğer yoksa)
        try:
            c.execute("ALTER TABLE urunler ADD COLUMN kategori TEXT")
        except sqlite3.OperationalError:
            pass  # Sütun zaten varsa hata verme

        conn.commit()

@app.route("/")
def index():
    return render_template("index.html")

@app.route("/hakkimizda")
def hakkimizda():
    return render_template("hakkimizda.html")

@app.route("/hizmetler")
def hizmetler():
    return render_template("hizmetler.html")

@app.route("/urunler")
def urunler():
    secili_kategori = request.args.get("kategori", "")
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("SELECT isim FROM kategoriler")
        kategoriler = c.fetchall()

        if secili_kategori:
            c.execute("SELECT * FROM urunler WHERE kategori = ?", (secili_kategori,))
        else:
            c.execute("SELECT * FROM urunler")

        urunler = c.fetchall()

    return render_template("urunler.html", urunler=urunler, kategoriler=kategoriler, secili_kategori=secili_kategori)

@app.route("/iletisim", methods=["GET", "POST"])
def iletisim():
    if request.method == "POST":
        # Buraya e-posta gönderme veya kayıt eklenebilir
        return redirect("/")
    return render_template("iletisim.html")

@app.route("/admin", methods=["GET", "POST"])
def admin():
    if "kullanici" not in session:
        return redirect("/login")
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("SELECT * FROM kategoriler")
        kategoriler = c.fetchall()

        if request.method == "POST":
            isim = request.form["isim"]
            aciklama = request.form["aciklama"]
            fiyat = request.form["fiyat"]
            kategori = request.form["kategori"]
            dosya = request.files["resim_dosya"]
            resim_url = ""

            if dosya and allowed_file(dosya.filename):
                filename = secure_filename(dosya.filename)
                yol = os.path.join(app.config["UPLOAD_FOLDER"], filename)
                dosya.save(yol)
                resim_url = yol

            c.execute("INSERT INTO urunler (isim, aciklama, fiyat, resim_url, kategori) VALUES (?, ?, ?, ?, ?)",
                      (isim, aciklama, fiyat, resim_url, kategori))
            conn.commit()
            return redirect("/admin")

        c.execute("SELECT * FROM urunler")
        urunler = c.fetchall()

    return render_template("admin.html", urunler=urunler, kategoriler=kategoriler)

@app.route("/admin/sil/<int:id>")
def urun_sil(id):
    if "kullanici" not in session:
        return redirect("/login")
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("DELETE FROM urunler WHERE id = ?", (id,))
        conn.commit()
    return redirect("/admin")

@app.route("/admin/duzenle/<int:id>", methods=["GET", "POST"])
def urun_duzenle(id):
    if "kullanici" not in session:
        return redirect("/login")
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        if request.method == "POST":
            isim = request.form["isim"]
            aciklama = request.form["aciklama"]
            fiyat = request.form["fiyat"]
            kategori = request.form["kategori"]
            dosya = request.files.get("resim_dosya")

            # Mevcut resim URL'sini al
            c.execute("SELECT resim_url FROM urunler WHERE id = ?", (id,))
            mevcut_resim = c.fetchone()[0]
            resim_url = mevcut_resim  # Varsayılan olarak mevcut resmi koru

            # Yeni dosya yüklendiyse
            if dosya and allowed_file(dosya.filename):
                filename = secure_filename(dosya.filename)
                yol = os.path.join(app.config["UPLOAD_FOLDER"], filename)
                dosya.save(yol)
                resim_url = yol

            c.execute("""
                UPDATE urunler
                SET isim = ?, aciklama = ?, fiyat = ?, resim_url = ?, kategori = ?
                WHERE id = ?
            """, (isim, aciklama, fiyat, resim_url, kategori, id))
            conn.commit()
            return redirect("/admin")
        else:
            c.execute("SELECT * FROM urunler WHERE id = ?", (id,))
            urun = c.fetchone()
            c.execute("SELECT * FROM kategoriler")
            kategoriler = c.fetchall()
            if urun:
                return render_template("duzenle.html", urun=urun, kategoriler=kategoriler)
            else:
                return "Ürün bulunamadı", 404

@app.route("/admin/kategoriler", methods=["GET", "POST"])
def kategoriler():
    if "kullanici" not in session:
        return redirect("/login")
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        if request.method == "POST":
            yeni_kategori = request.form["kategori_adi"].strip()
            if yeni_kategori:
                try:
                    c.execute("INSERT INTO kategoriler (isim) VALUES (?)", (yeni_kategori,))
                    conn.commit()
                except sqlite3.IntegrityError:
                    pass  # Aynı isimde kategori eklenirse hata verme
            return redirect("/admin/kategoriler")
        c.execute("SELECT * FROM kategoriler")
        kategoriler = c.fetchall()
    return render_template("kategoriler.html", kategoriler=kategoriler)

@app.route("/register", methods=["GET", "POST"])
def register():
    if request.method == "POST":
        kullaniciadi = request.form["kullaniciadi"]
        sifre = request.form["sifre"]
        sifre_hash = bcrypt.generate_password_hash(sifre).decode("utf-8")
        with sqlite3.connect("database.db") as conn:
            c = conn.cursor()
            try:
                c.execute("INSERT INTO kullanicilar (kullaniciadi, sifre) VALUES (?, ?)", (kullaniciadi, sifre_hash))
                conn.commit()
                return redirect("/login")
            except sqlite3.IntegrityError:
                return "Bu kullanıcı adı zaten alınmış."
    return render_template("register.html")

@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        kullaniciadi = request.form["kullaniciadi"]
        sifre = request.form["sifre"]
        with sqlite3.connect("database.db") as conn:
            c = conn.cursor()
            c.execute("SELECT * FROM kullanicilar WHERE kullaniciadi = ?", (kullaniciadi,))
            user = c.fetchone()
            if user and bcrypt.check_password_hash(user[2], sifre):
                session["kullanici"] = kullaniciadi
                return redirect("/admin")
            else:
                return "Hatalı kullanıcı adı veya şifre"
    return render_template("login.html")

@app.route("/logout")
def logout():
    session.pop("kullanici", None)
    return redirect("/")

@app.route("/giris/google")
def google_login():
    if not google.authorized:
        return redirect(url_for("google.login"))

    resp = google.get("/oauth2/v2/userinfo")
    if not resp.ok:
        return "Google'dan bilgi alınamadı!", 400

    user_info = resp.json()
    session["kullanici"] = user_info["email"]
    return redirect("/admin")

@app.route("/gizlilik")
def gizlilik():
    return render_template("gizlilik.html")

@app.route("/kosullar")
def kosullar():
    return render_template("kosullar.html")

@app.route("/sifremi-unuttum", methods=["GET", "POST"])
def sifremi_unuttum():
    if request.method == "POST":
        email = request.form["email"]
        with sqlite3.connect("database.db") as conn:
            c = conn.cursor()
            c.execute("SELECT * FROM kullanicilar WHERE kullaniciadi = ?", (email,))
            user = c.fetchone()
            if user:
                token = s.dumps(email, salt="sifre-sifirlama")
                link = url_for("sifre_sifirla", token=token, _external=True)
                msg = Message("Şenkal - Şifre Sıfırlama Linkiniz", recipients=[email])
                msg.body = f"""
Merhaba,

Şenkal Isıtma ve Soğutma Sistemleri hesabınız için şifre sıfırlama talebinde bulundunuz.

Şifrenizi sıfırlamak için aşağıdaki bağlantıya tıklayın:
{link}

Bu bağlantı 30 dakika geçerlidir.

Eğer bu talebi siz yapmadıysanız, bu e-postayı görmezden gelebilirsiniz.

İyi günler,
Şenkal Teknik Destek
"""
                try:
                    mail.send(msg)
                    return render_template("sifremi_unuttum.html", mesaj="E-posta gönderildi! Gelen kutunu kontrol et.", tip="basari")
                except Exception as e:
                    return render_template("sifremi_unuttum.html", mesaj="E-posta gönderilemedi. Lütfen daha sonra tekrar deneyin.", tip="hata")
            else:
                return render_template("sifremi_unuttum.html", mesaj="Bu e-posta sistemde kayıtlı değil.", tip="hata")
    return render_template("sifremi_unuttum.html")

@app.route("/sifre-sifirla/<token>", methods=["GET", "POST"])
def sifre_sifirla(token):
    try:
        email = s.loads(token, salt="sifre-sifirlama", max_age=1800)  # 30 dakika geçerli
    except:
        return render_template("sifre_sifirla.html", mesaj="Token süresi dolmuş veya geçersiz. Lütfen yeni bir şifre sıfırlama talebi oluşturun.", tip="hata")

    if request.method == "POST":
        yeni_sifre = request.form["yeni_sifre"]
        if len(yeni_sifre) < 6:
            return render_template("sifre_sifirla.html", email=email, mesaj="Şifre en az 6 karakter olmalıdır.", tip="hata")

        sifre_hash = bcrypt.generate_password_hash(yeni_sifre).decode("utf-8")
        with sqlite3.connect("database.db") as conn:
            c = conn.cursor()
            c.execute("UPDATE kullanicilar SET sifre = ? WHERE kullaniciadi = ?", (sifre_hash, email))
            conn.commit()
        return render_template("sifre_sifirla.html", mesaj="Şifren başarıyla güncellendi! Artık yeni şifrenle giriş yapabilirsin.", tip="basari")

    return render_template("sifre_sifirla.html", email=email)

if __name__ == "__main__":
    init_db()
    app.run(debug=True)
