from flask import Flask, render_template, request, redirect
import sqlite3
import os
from werkzeug.utils import secure_filename

app = Flask(__name__)

UPLOAD_FOLDER = "static/uploads"
ALLOWED_EXTENSIONS = {"png", "jpg", "jpeg", "gif"}
app.config["UPLOAD_FOLDER"] = UPLOAD_FOLDER

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Veritabanı başlangıcı
def init_db():
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("""
            CREATE TABLE IF NOT EXISTS urunler (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                isim TEXT NOT NULL,
                aciklama TEXT,
                fiyat REAL,
                resim_url TEXT,
                kategori TEXT
            )
        """)
        c.execute("""
            CREATE TABLE IF NOT EXISTS kategoriler (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                isim TEXT NOT NULL UNIQUE
            )
        """)

        # Mevcut tabloya kategori sütunu ekle (eğer yoksa)
        try:
            c.execute("ALTER TABLE urunler ADD COLUMN kategori TEXT")
        except sqlite3.OperationalError:
            pass  # Sütun zaten varsa hata verme

        conn.commit()

@app.route("/")
def index():
    return render_template("index.html")

@app.route("/hakkimizda")
def hakkimizda():
    return render_template("hakkimizda.html")

@app.route("/hizmetler")
def hizmetler():
    return render_template("hizmetler.html")

@app.route("/urunler")
def urunler():
    secili_kategori = request.args.get("kategori", "")
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("SELECT isim FROM kategoriler")
        kategoriler = c.fetchall()

        if secili_kategori:
            c.execute("SELECT * FROM urunler WHERE kategori = ?", (secili_kategori,))
        else:
            c.execute("SELECT * FROM urunler")

        urunler = c.fetchall()

    return render_template("urunler.html", urunler=urunler, kategoriler=kategoriler, secili_kategori=secili_kategori)

@app.route("/iletisim", methods=["GET", "POST"])
def iletisim():
    if request.method == "POST":
        # Buraya e-posta gönderme veya kayıt eklenebilir
        return redirect("/")
    return render_template("iletisim.html")

@app.route("/admin", methods=["GET", "POST"])
def admin():
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("SELECT * FROM kategoriler")
        kategoriler = c.fetchall()

        if request.method == "POST":
            isim = request.form["isim"]
            aciklama = request.form["aciklama"]
            fiyat = request.form["fiyat"]
            kategori = request.form["kategori"]
            dosya = request.files["resim_dosya"]
            resim_url = ""

            if dosya and allowed_file(dosya.filename):
                filename = secure_filename(dosya.filename)
                yol = os.path.join(app.config["UPLOAD_FOLDER"], filename)
                dosya.save(yol)
                resim_url = yol

            c.execute("INSERT INTO urunler (isim, aciklama, fiyat, resim_url, kategori) VALUES (?, ?, ?, ?, ?)",
                      (isim, aciklama, fiyat, resim_url, kategori))
            conn.commit()
            return redirect("/admin")

        c.execute("SELECT * FROM urunler")
        urunler = c.fetchall()

    return render_template("admin.html", urunler=urunler, kategoriler=kategoriler)

@app.route("/admin/sil/<int:id>")
def urun_sil(id):
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        c.execute("DELETE FROM urunler WHERE id = ?", (id,))
        conn.commit()
    return redirect("/admin")

@app.route("/admin/duzenle/<int:id>", methods=["GET", "POST"])
def urun_duzenle(id):
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        if request.method == "POST":
            isim = request.form["isim"]
            aciklama = request.form["aciklama"]
            fiyat = request.form["fiyat"]
            kategori = request.form["kategori"]
            dosya = request.files.get("resim_dosya")

            # Mevcut resim URL'sini al
            c.execute("SELECT resim_url FROM urunler WHERE id = ?", (id,))
            mevcut_resim = c.fetchone()[0]
            resim_url = mevcut_resim  # Varsayılan olarak mevcut resmi koru

            # Yeni dosya yüklendiyse
            if dosya and allowed_file(dosya.filename):
                filename = secure_filename(dosya.filename)
                yol = os.path.join(app.config["UPLOAD_FOLDER"], filename)
                dosya.save(yol)
                resim_url = yol

            c.execute("""
                UPDATE urunler
                SET isim = ?, aciklama = ?, fiyat = ?, resim_url = ?, kategori = ?
                WHERE id = ?
            """, (isim, aciklama, fiyat, resim_url, kategori, id))
            conn.commit()
            return redirect("/admin")
        else:
            c.execute("SELECT * FROM urunler WHERE id = ?", (id,))
            urun = c.fetchone()
            c.execute("SELECT * FROM kategoriler")
            kategoriler = c.fetchall()
            if urun:
                return render_template("duzenle.html", urun=urun, kategoriler=kategoriler)
            else:
                return "Ürün bulunamadı", 404

@app.route("/admin/kategoriler", methods=["GET", "POST"])
def kategoriler():
    with sqlite3.connect("database.db") as conn:
        c = conn.cursor()
        if request.method == "POST":
            yeni_kategori = request.form["kategori_adi"].strip()
            if yeni_kategori:
                try:
                    c.execute("INSERT INTO kategoriler (isim) VALUES (?)", (yeni_kategori,))
                    conn.commit()
                except sqlite3.IntegrityError:
                    pass  # Aynı isimde kategori eklenirse hata verme
            return redirect("/admin/kategoriler")
        c.execute("SELECT * FROM kategoriler")
        kategoriler = c.fetchall()
    return render_template("kategoriler.html", kategoriler=kategoriler)

if __name__ == "__main__":
    init_db()
    app.run(debug=True)
