{% extends "base.html" %}

{% block content %}
<div class="card">
    <h2><i class="fas fa-tags"></i> <PERSON>gori Yönetimi</h2>
    <p><PERSON><PERSON><PERSON>n kategorilerini buradan yönetebilirsiniz</p>
</div>

<div class="card">
    <h3><i class="fas fa-plus"></i> <PERSON><PERSON>gori <PERSON></h3>
    <form method="POST">
        <label><i class="fas fa-tag"></i> Kategori Adı:</label>
        <input type="text" name="kategori_adi" placeholder="Örn: Klima, Buzdolabı, Kombi" required>
        <button type="submit" style="background: #28a745;">
            <i class="fas fa-plus"></i> Ekle
        </button>
    </form>
</div>

<div class="card">
    <h3><i class="fas fa-list"></i> Mevcut Kategoriler</h3>
    {% if kate<PERSON><PERSON> %}
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
            {% for kategori in kategoriler %}
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                    <i class="fas fa-tag"></i>
                    <strong>{{ kategori[1] }}</strong>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div style="text-align: center; padding: 30px; color: #666;">
            <i class="fas fa-info-circle" style="font-size: 2rem; margin-bottom: 10px;"></i>
            <p>Henüz kategori eklenmemiş.</p>
        </div>
    {% endif %}
</div>

<div style="text-align: center; margin-top: 20px;">
    <a href="/admin" style="background: #6c757d; color: white; text-decoration: none; padding: 12px 20px; border-radius: 8px; display: inline-block;">
        <i class="fas fa-arrow-left"></i> Admin Paneline Dön
    </a>
</div>
{% endblock %}
