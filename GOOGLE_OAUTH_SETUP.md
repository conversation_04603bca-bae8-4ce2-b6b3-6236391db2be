# Google OAuth Kurulum Rehberi

## 🔧 Google Console Kurulumu

### 1. Google Cloud Console'a Git
- https://console.cloud.google.com/ adresine git
- Yeni proje oluştur veya mevcut projeyi seç

### 2. OAuth Consent Screen Ayarla
- <PERSON> menüden "APIs & Services" > "OAuth consent screen"
- User Type: External seç
- App name: "Şenkal Admin Panel"
- User support email: <EMAIL>
- Developer contact: <EMAIL>

### 3. Credentials Oluştur
- "APIs & Services" > "Credentials"
- "+ CREATE CREDENTIALS" > "OAuth 2.0 Client IDs"
- Application type: Web application
- Name: "Şenkal Web App"
- Authorized redirect URIs:
  - http://localhost:5000/login/google/authorized
  - https://yourdomain.com/login/google/authorized

### 4. Client ID ve Secret Al
- Client ID ve Client Secret'i kopyala
- app.py dosyasında güncelle:

```python
google_bp = make_google_blueprint(
    client_id="YOUR_GOOGLE_CLIENT_ID_HERE",
    client_secret="YOUR_GOOGLE_CLIENT_SECRET_HERE",
    scope=["profile", "email"],
    redirect_url="/login/google/authorized"
)
```

## 📦 Gerekli Paket Kurulumu

```bash
pip install Flask-Dance
```

## 🔒 Güvenlik Notları

### Production Ortamı İçin:
1. Environment variables kullan:
```python
import os
client_id = os.environ.get("GOOGLE_CLIENT_ID")
client_secret = os.environ.get("GOOGLE_CLIENT_SECRET")
```

2. HTTPS kullan (zorunlu)
3. Güvenli domain kullan
4. Secret key'i güvenli tut

## ✅ Test Etme

1. Uygulamayı başlat: `python app.py`
2. `/login` sayfasına git
3. "Google ile Giriş Yap" butonuna tıkla
4. Google hesabınla giriş yap
5. Admin paneline yönlendirilmelisin

## 🚨 Sorun Giderme

### Yaygın Hatalar:
- **redirect_uri_mismatch**: Redirect URI'yi Google Console'da kontrol et
- **invalid_client**: Client ID/Secret'i kontrol et
- **access_denied**: OAuth consent screen'i kontrol et

### Debug İçin:
```python
app.config['OAUTHLIB_INSECURE_TRANSPORT'] = True  # Sadece development için
```

## 📋 Özellikler

✅ Google ile tek tıkla giriş
✅ Otomatik kullanıcı kaydı
✅ Email tabanlı kimlik doğrulama
✅ Güvenli session yönetimi
✅ Normal giriş ile uyumlu
✅ Admin panel koruması
