{% extends "base.html" %}

{% block content %}
<div class="card">
    <h2><i class="fas fa-edit"></i> <PERSON><PERSON><PERSON><PERSON><PERSON></h2>
    <p><PERSON><PERSON>ün bilgilerini güncelleyin</p>
</div>

<div class="card">
    <form method="POST" enctype="multipart/form-data">
        <label><i class="fas fa-tag"></i> Ürün İsmi:</label>
        <input type="text" name="isim" value="{{ urun[1] }}" placeholder="Örn: Samsung Buzdolabı RT50K6000S8" required>
        
        <label><i class="fas fa-info-circle"></i> Açıklama:</label>
        <input type="text" name="aciklama" value="{{ urun[2] if urun[2] else '' }}" placeholder="Ürün özellikleri, renk, boyut vb.">
        
        <label><i class="fas fa-lira-sign"></i> Fiyat (TL):</label>
        <input type="number" name="fiyat" step="0.01" value="{{ urun[3] if urun[3] else '' }}" placeholder="0.00">
        
        <label><i class="fas fa-image"></i> Resim Yükle:</label>
        <input type="file" name="resim_dosya" accept="image/*">

        <label><i class="fas fa-tags"></i> Kategori:</label>
        <select name="kategori" required>
            <option value="">Kategori Seçin</option>
            {% for kategori in kategoriler %}
                <option value="{{ kategori[1] }}" {% if urun[5] == kategori[1] %}selected{% endif %}>{{ kategori[1] }}</option>
            {% endfor %}
        </select>
        
        <div style="margin-top: 20px;">
            <button type="submit" style="background: #28a745; margin-right: 10px;">
                <i class="fas fa-save"></i> Kaydet
            </button>
            <a href="/admin" style="background: #6c757d; color: white; text-decoration: none; padding: 12px 20px; border-radius: 8px; display: inline-block;">
                <i class="fas fa-arrow-left"></i> İptal
            </a>
        </div>
    </form>
</div>

{% if urun[4] %}
<div class="card">
    <h3><i class="fas fa-eye"></i> Mevcut Resim Önizlemesi</h3>
    <img src="{{ urun[4] }}" alt="{{ urun[1] }}" style="max-width: 300px; height: auto; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
</div>
{% endif %}
{% endblock %}
