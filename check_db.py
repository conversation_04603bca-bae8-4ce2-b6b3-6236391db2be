import sqlite3

# Veritabanını kontrol et
conn = sqlite3.connect('database.db')
c = conn.cursor()

print("=== VERİTABANI KONTROL ===")

# Kullanicilar tablosu yapısını kontrol et
try:
    c.execute("PRAGMA table_info(kullanicilar)")
    columns = c.fetchall()
    print("\nKullanicilar tablosu sütunları:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # Mevcut kullanıcıları göster
    c.execute("SELECT * FROM kullanicilar")
    users = c.fetchall()
    print(f"\nMevcut kullanıcı sayısı: {len(users)}")
    for user in users:
        print(f"  {user}")
        
except Exception as e:
    print(f"Hata: {e}")

conn.close()
