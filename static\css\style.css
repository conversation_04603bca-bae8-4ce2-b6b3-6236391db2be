@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap');

* {
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    background: linear-gradient(to bottom right, #e6f0ff, #ffffff);
    margin: 0;
    padding: 0;
    color: #333;
}

header {
    background-color: #003366;
    color: white;
    padding: 20px 0;
    text-align: center;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    position: relative;
}

header .hamburger {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
}

nav {
    margin-top: 10px;
}

nav a {
    color: white;
    margin: 0 15px;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
}

nav a:hover {
    color: #ffcc00;
}

main {
    padding: 40px 20px;
    max-width: 1000px;
    margin: auto;
    animation: fadeIn 1s ease-in;
}

footer {
    background-color: #f0f0f0;
    text-align: center;
    padding: 20px;
    margin-top: 40px;
    font-size: 14px;
    color: #555;
}

form {
    background-color: #ffffff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

form input, form textarea {
    width: 100%;
    padding: 12px;
    margin: 10px 0;
    border: 1px solid #ccc;
    border-radius: 8px;
    transition: 0.3s ease;
}

form input:focus, form textarea:focus {
    border-color: #0077cc;
    outline: none;
}

button {
    background-color: #0077cc;
    color: white;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
}

button:hover {
    background-color: #005fa3;
}

ul {
    list-style: none;
    padding: 0;
}

li {
    background: #fff;
    margin: 10px 0;
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

li:hover {
    transform: scale(1.02);
}

/* Card Stilleri */
.card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    padding: 20px;
    margin: 20px 0;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.service-card {
    text-align: center;
    padding: 30px 20px;
}

.service-card i {
    font-size: 3rem;
    color: #0077cc;
    margin-bottom: 15px;
}

.product-card {
    border-left: 4px solid #0077cc;
}

.product-card h3 {
    color: #003366;
    margin-bottom: 10px;
}

.price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #0077cc;
}

/* Ürün Kartları Hover Efekti */
div[style*="width: 280px"]:hover {
    transform: translateY(-10px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.2) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    header h1 {
        font-size: 1.2rem;
        margin: 0;
        padding-right: 60px;
    }

    nav a {
        display: block;
        margin: 5px 0;
    }

    main {
        padding: 20px 10px;
    }

    .card-grid {
        grid-template-columns: 1fr;
    }

    /* Ürün kartları mobilde */
    div[style*="width: 280px"] {
        width: 100% !important;
        max-width: 350px;
        margin: 0 auto 20px auto;
    }

    /* İletişim sayfası responsive */
    div[style*="grid-template-columns: 2fr 1fr"] {
        display: block !important;
    }

    /* Admin sayfası responsive */
    div[style*="grid-template-columns: 1fr 1fr"] {
        display: block !important;
    }

    /* Hakkımızda sayfası responsive */
    div[style*="grid-template-columns: 2fr 1fr"] {
        display: block !important;
    }

    /* Mobil menü hover efekti */
    .mobile-nav a:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

@keyframes fadeIn {
    from {opacity: 0;}
    to {opacity: 1;}
}

/* Hamburger Menü Animasyonu */
.hamburger.active div:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.hamburger.active div:nth-child(2) {
    opacity: 0;
}

.hamburger.active div:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

/* Mobil menü slide animasyonu */
.mobile-nav {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.mobile-nav.active {
    max-height: 300px;
}
