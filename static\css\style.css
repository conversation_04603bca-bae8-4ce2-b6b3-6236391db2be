/* Şenkal Beyaz Eşya Servisi - Custom CSS */

/* <PERSON><PERSON> */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    background-color: #f8f9fa;
}

/* Navbar Özelleştirmeleri */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 5px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #ffc107 !important;
}

/* Card Hover Efektleri */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* Jumbotron Stili */
.jumbotron {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 15px;
}

/* <PERSON><PERSON> Özelleştirmeleri */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
}

/* Form Stilleri */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Footer Stili */
footer {
    margin-top: auto;
}

/* Özel Renkler */
.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

.text-primary {
    color: #007bff !important;
}

/* Responsive Düzenlemeler */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .jumbotron {
        padding: 2rem 1rem;
    }
    
    .jumbotron h1 {
        font-size: 2rem;
    }
}

/* Animasyonlar */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.card {
    animation: fadeIn 0.6s ease-out;
}

/* Tablo Stilleri */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table th {
    background-color: #343a40;
    color: white;
    font-weight: 600;
    border: none;
}

.table td {
    vertical-align: middle;
    border-color: #dee2e6;
}

/* Badge Stilleri */
.badge {
    font-size: 0.8rem;
    padding: 0.5rem 0.8rem;
}

/* Alert Özelleştirmeleri */
.alert {
    border-radius: 10px;
    border: none;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* Özel Utility Sınıfları */
.text-shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.rounded-lg {
    border-radius: 15px !important;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hover Efektleri */
.hover-shadow:hover {
    box-shadow: 0 8px 16px rgba(0,0,0,0.2);
    transition: box-shadow 0.3s ease;
}

/* Özel Grid Düzenlemeleri */
.equal-height {
    display: flex;
    flex-wrap: wrap;
}

.equal-height .card {
    height: 100%;
}

/* Print Stilleri */
@media print {
    .navbar, .btn, footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
