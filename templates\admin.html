{% extends "base.html" %}

{% block title %}Yönetim Paneli - Şenkal Beyaz Eşya Servisi{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1>🔧 Yönetim Paneli</h1>
        <p class="lead"><PERSON><PERSON><PERSON>n ekleme ve yönetim sistemi</p>
        <hr>
    </div>
</div>

<!-- Ürün Ekleme Formu -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4>➕ <PERSON><PERSON></h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="isim" class="form-label">Ürün Adı *</label>
                        <input type="text" class="form-control" id="isim" name="isim" required 
                               placeholder="Örn: Samsung Buzdolabı RT50K6000S8">
                    </div>
                    
                    <div class="mb-3">
                        <label for="aciklama" class="form-label">Açıklama</label>
                        <textarea class="form-control" id="aciklama" name="aciklama" rows="4" 
                                  placeholder="Ürün özellikleri, renk, boyut vb. bilgiler..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="fiyat" class="form-label">Fiyat (₺)</label>
                        <input type="number" class="form-control" id="fiyat" name="fiyat" step="0.01" min="0"
                               placeholder="0.00">
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-lg w-100">
                        💾 Ürün Ekle
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Hızlı İstatistikler -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h4>📊 İstatistikler</h4>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h3 class="text-primary">{{ urunler|length }}</h3>
                        <p>Toplam Ürün</p>
                    </div>
                    <div class="col-6">
                        <h3 class="text-success">
                            {% set toplam_fiyat = urunler|selectattr(3)|map(attribute=3)|sum %}
                            {{ "%.0f"|format(toplam_fiyat) }} ₺
                        </h3>
                        <p>Toplam Değer</p>
                    </div>
                </div>
                
                <hr>
                
                <h6>Hızlı İşlemler:</h6>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('urunler') }}" class="btn btn-outline-primary btn-sm">
                        👁️ Ürünleri Görüntüle
                    </a>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-secondary btn-sm">
                        🏠 Ana Sayfaya Dön
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Mevcut Ürünler Listesi -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h4>📦 Mevcut Ürünler ({{ urunler|length }} adet)</h4>
            </div>
            <div class="card-body">
                {% if urunler %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Ürün Adı</th>
                                <th>Açıklama</th>
                                <th>Fiyat</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for urun in urunler %}
                            <tr>
                                <td><span class="badge bg-primary">#{{ urun[0] }}</span></td>
                                <td><strong>{{ urun[1] }}</strong></td>
                                <td>
                                    {% if urun[2] %}
                                        {% if urun[2]|length > 50 %}
                                            {{ urun[2][:50] }}...
                                        {% else %}
                                            {{ urun[2] }}
                                        {% endif %}
                                    {% else %}
                                        <em class="text-muted">Açıklama yok</em>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if urun[3] %}
                                        <span class="text-success fw-bold">{{ "%.2f"|format(urun[3]) }} ₺</span>
                                    {% else %}
                                        <em class="text-muted">Fiyat belirtilmemiş</em>
                                    {% endif %}
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            onclick="alert('Silme özelliği yakında eklenecek!')">
                                        🗑️ Sil
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <h5 class="text-muted">Henüz ürün eklenmemiş</h5>
                    <p class="text-muted">Yukarıdaki formu kullanarak ilk ürününüzü ekleyin.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Uyarı Mesajı -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-warning">
            <h6>⚠️ Güvenlik Uyarısı</h6>
            <p class="mb-0">Bu yönetim paneli henüz şifre korumalı değildir. Gelecek güncellemelerde kimlik doğrulama sistemi eklenecektir.</p>
        </div>
    </div>
</div>
{% endblock %}
