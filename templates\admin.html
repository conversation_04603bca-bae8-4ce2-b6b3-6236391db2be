{% extends "base.html" %}

{% block content %}
<div class="card">
    <h2><i class="fas fa-cog"></i> Admin Paneli</h2>
    <p><PERSON>r<PERSON>n ekleme ve y<PERSON> siste<PERSON></p>
    <div style="background: #e8f5e8; padding: 10px; border-radius: 5px; margin: 15px 0;">
        <small style="color: #28a745;"><i class="fas fa-user"></i> <PERSON><PERSON> gel<PERSON>, <strong>{{ session.kullanici }}</strong></small>
        <a href="/logout" style="float: right; color: #dc3545; text-decoration: none; font-size: 0.9rem;">
            <i class="fas fa-sign-out-alt"></i> Çık<PERSON>ş Yap
        </a>
    </div>
    <div style="margin-top: 15px;">
        <a href="/admin/kategoriler" style="background: #17a2b8; color: white; text-decoration: none; padding: 10px 15px; border-radius: 5px; display: inline-block; margin-right: 10px;">
            <i class="fas fa-tags"></i> <PERSON>gori Yönetimi
        </a>
    </div>
</div>

<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
    <div class="card">
        <h3><i class="fas fa-plus-circle"></i> Yeni Ürün Ekle</h3>
        <form method="POST" enctype="multipart/form-data">
            <label><i class="fas fa-tag"></i> Ürün İsmi:</label>
            <input type="text" name="isim" placeholder="Örn: Samsung Buzdolabı RT50K6000S8" required>

            <label><i class="fas fa-info-circle"></i> Açıklama:</label>
            <input type="text" name="aciklama" placeholder="Ürün özellikleri, renk, boyut vb.">

            <label><i class="fas fa-lira-sign"></i> Fiyat (TL):</label>
            <input type="number" name="fiyat" step="0.01" placeholder="0.00">

            <label><i class="fas fa-image"></i> Resim Yükle:</label>
            <input type="file" name="resim_dosya" accept="image/*">

            <label><i class="fas fa-tags"></i> Kategori:</label>
            <select name="kategori" required>
                <option value="">Kategori Seçin</option>
                {% for kategori in kategoriler %}
                    <option value="{{ kategori[1] }}">{{ kategori[1] }}</option>
                {% endfor %}
            </select>

            <button type="submit">
                <i class="fas fa-save"></i> Ürün Ekle
            </button>
        </form>
    </div>

    <div class="card">
        <h3><i class="fas fa-chart-bar"></i> İstatistikler</h3>
        <div style="text-align: center;">
            <div style="display: inline-block; margin: 10px 20px;">
                <div style="font-size: 2rem; font-weight: bold; color: #0077cc;">{{ urunler|length }}</div>
                <div>Toplam Ürün</div>
            </div>
            <div style="display: inline-block; margin: 10px 20px;">
                <div style="font-size: 2rem; font-weight: bold; color: #28a745;">
                    {% set toplam_fiyat = urunler|selectattr(3)|map(attribute=3)|sum %}
                    {{ "%.0f"|format(toplam_fiyat) }} TL
                </div>
                <div>Toplam Değer</div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <h3><i class="fas fa-boxes"></i> Mevcut Ürünler ({{ urunler|length }} adet)</h3>
    {% if urunler %}
    <div class="card-grid">
        {% for urun in urunler %}
        <div class="card product-card" style="margin: 10px 0;">
            {% if urun[4] %}
            <img src="{{ urun[4] }}" alt="{{ urun[1] }}" style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px; margin-bottom: 10px;">
            {% else %}
            <img src="https://via.placeholder.com/300x150?text=Ürün+Resmi" alt="{{ urun[1] }}" style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px; margin-bottom: 10px;">
            {% endif %}
            <h4><i class="fas fa-cube"></i> {{ urun[1] }}</h4>
            <p>{{ urun[2] if urun[2] else 'Açıklama bulunmuyor.' }}</p>
            {% if urun[5] %}
            <div style="background: #17a2b8; color: white; padding: 3px 6px; border-radius: 10px; font-size: 0.7rem; display: inline-block; margin: 5px 0;">
                <i class="fas fa-tags"></i> {{ urun[5] }}
            </div>
            {% endif %}
            {% if urun[3] %}
            <div class="price">{{ "%.2f"|format(urun[3]) }} TL</div>
            {% endif %}
            <small style="color: #666;">ID: #{{ urun[0] }}</small>
            <div style="margin-top: 10px;">
                <a href="/admin/duzenle/{{ urun[0] }}"
                   style="background: #28a745; color: white; text-decoration: none; font-size: 0.8rem; padding: 5px 10px; border-radius: 5px; display: inline-block; margin-right: 5px;">
                    <i class="fas fa-edit"></i> Düzenle
                </a>
                <a href="/admin/sil/{{ urun[0] }}"
                   style="background: #dc3545; color: white; text-decoration: none; font-size: 0.8rem; padding: 5px 10px; border-radius: 5px; display: inline-block;"
                   onclick="return confirm('Bu ürünü silmek istediğinizden emin misiniz?')">
                    <i class="fas fa-trash"></i> Sil
                </a>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div style="text-align: center; padding: 40px; color: #666;">
        <i class="fas fa-box-open" style="font-size: 3rem; margin-bottom: 15px;"></i>
        <h4>Henüz ürün eklenmemiş</h4>
        <p>Yukarıdaki formu kullanarak ilk ürününüzü ekleyin.</p>
    </div>
    {% endif %}
</div>

<div class="card" style="background: #fff3cd; border-left: 4px solid #ffc107;">
    <h4><i class="fas fa-exclamation-triangle" style="color: #856404;"></i> Güvenlik Uyarısı</h4>
    <p style="margin: 0; color: #856404;">Bu yönetim paneli henüz şifre korumalı değildir. Gelecek güncellemelerde kimlik doğrulama sistemi eklenecektir.</p>
</div>
{% endblock %}
