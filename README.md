# Şenkal Beyaz Eşya Servisi Web Sitesi

Bu proje, Şenkal Beyaz Eşya Servisi için geliştirilmiş modern bir web sitesidir. Flask framework'ü kullanılarak geliştirilmiştir.

## 🚀 Özellikler

- **Ana Sayfa**: Şirket tanıtımı ve hizmet özetleri
- **Hakkımızda**: Şirket bilgileri, misyon, vizyon
- **Hizmetler**: Detaylı hizmet listesi (klima, buzdolabı, kombi vb.)
- **Ürünler**: Dinamik ürün listesi (veritabanından çekilen)
- **İletişim**: İletişim formu ve bilgileri
- **Yönetim Paneli**: Ürün ekleme/yönetim sistemi

## 📁 Proje <PERSON>

```
senkal/
├── app.py                 # Ana Flask uygulaması
├── database.db           # SQLite veritabanı (otomatik oluşur)
├── templates/             # HTML şablonları
│   ├── base.html         # Ana şablon
│   ├── index.html        # Ana sayfa
│   ├── hakkimizda.html   # Hakkımızda sayfası
│   ├── hizmetler.html    # Hizmetler sayfası
│   ├── urunler.html      # Ürünler sayfası
│   ├── iletisim.html     # İletişim sayfası
│   └── admin.html        # Yönetim paneli
├── static/               # Statik dosyalar
│   └── css/
│       └── style.css     # Özel CSS stilleri
└── README.md             # Bu dosya
```

## 🛠️ Kurulum

### Gereksinimler
- Python 3.7+
- Flask

### Adımlar

1. **Gerekli paketleri yükleyin:**
```bash
pip install flask
```

2. **Uygulamayı çalıştırın:**
```bash
python app.py
```

3. **Tarayıcınızda açın:**
```
http://localhost:5000
```

## 📱 Sayfalar

### Ana Sayfa (/)
- Şirket tanıtımı
- Hizmet özetleri
- İletişim bilgileri

### Hakkımızda (/hakkimizda)
- Şirket hikayesi
- Misyon ve vizyon
- Değerler
- İletişim bilgileri

### Hizmetler (/hizmetler)
- Klima servisi
- Buzdolabı tamiri
- Çamaşır makinesi servisi
- Kombi servisi
- Fiyat bilgileri

### Ürünler (/urunler)
- Dinamik ürün listesi
- Ürün kategorileri
- Marka bilgileri

### İletişim (/iletisim)
- İletişim formu
- İletişim bilgileri
- Konum haritası
- Hızlı iletişim butonları

### Yönetim Paneli (/admin)
- Ürün ekleme formu
- Mevcut ürünler listesi
- İstatistikler

## 🗄️ Veritabanı

Proje SQLite veritabanı kullanır. Veritabanı otomatik olarak oluşturulur.

### Tablo Yapısı

**urunler** tablosu:
- `id` (INTEGER, PRIMARY KEY, AUTOINCREMENT)
- `isim` (TEXT, NOT NULL) - Ürün adı
- `aciklama` (TEXT) - Ürün açıklaması
- `fiyat` (REAL) - Ürün fiyatı

## 🎨 Tasarım

- **Framework**: Bootstrap 5.1.3
- **Renkler**: Mavi tonları (profesyonel görünüm)
- **Responsive**: Mobil uyumlu tasarım
- **İkonlar**: Emoji tabanlı ikonlar

## 🔧 Geliştirme Notları

### Gelecek Özellikler
- [ ] Kullanıcı kimlik doğrulama sistemi
- [ ] Ürün silme/düzenleme özelliği
- [ ] İletişim formu e-posta entegrasyonu
- [ ] Resim yükleme sistemi
- [ ] Arama ve filtreleme
- [ ] Çoklu dil desteği

### Güvenlik
- Şu anda yönetim paneli şifre korumalı değildir
- Gelecek güncellemelerde kimlik doğrulama eklenecektir

## 📞 İletişim

**Şenkal Beyaz Eşya Servisi**
- Telefon: 0555 123 45 67
- E-posta: <EMAIL>
- Adres: Merkez Mahallesi, Tekniker Sokak No:15, İstanbul

## 📄 Lisans

Bu proje Şenkal Beyaz Eşya Servisi için özel olarak geliştirilmiştir.

---

**Geliştirici Notu**: Bu proje modüler yapıda tasarlanmıştır. Gelecekte ek özellikler kolayca entegre edilebilir.
