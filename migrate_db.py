import sqlite3
import os

def migrate_database():
    """Veritabanına yetki sütunu ekle"""
    
    print("🔧 VERİTABANI MİGRASYON BAŞLIYOR...")
    
    # Backup oluştur
    if os.path.exists('database.db'):
        import shutil
        shutil.copy2('database.db', 'database_backup.db')
        print("✅ Backup oluşturuldu: database_backup.db")
    
    conn = sqlite3.connect('database.db')
    c = conn.cursor()
    
    try:
        # Mevcut tablo yapısını kontrol et
        c.execute("PRAGMA table_info(kullanicilar)")
        columns = c.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"📋 Mevcut sütunlar: {column_names}")
        
        # Yetki sütunu var mı kontrol et
        if 'yetki' not in column_names:
            print("➕ 'yetki' sütunu ekleniyor...")
            
            # Yetki sütunu ekle (varsayılan: user)
            c.execute("ALTER TABLE kullanicilar ADD COLUMN yetki TEXT DEFAULT 'user'")
            
            # Mevcut kullanıcıları kontrol et
            c.execute("SELECT kullaniciadi FROM kullanicilar")
            users = c.fetchall()
            
            print(f"👥 Mevcut kullanıcılar: {len(users)}")
            
            # Admin e-posta listesi
            admin_emails = ["<EMAIL>", "<EMAIL>"]
            
            # Mevcut kullanıcıların yetkilerini güncelle
            for user in users:
                email = user[0]
                if email in admin_emails:
                    c.execute("UPDATE kullanicilar SET yetki = 'admin' WHERE kullaniciadi = ?", (email,))
                    print(f"👑 {email} -> admin yapıldı")
                else:
                    c.execute("UPDATE kullanicilar SET yetki = 'user' WHERE kullaniciadi = ?", (email,))
                    print(f"👤 {email} -> user yapıldı")
            
            conn.commit()
            print("✅ Yetki sütunu başarıyla eklendi!")
            
        else:
            print("ℹ️ 'yetki' sütunu zaten mevcut")
        
        # Son durumu göster
        c.execute("SELECT kullaniciadi, yetki FROM kullanicilar")
        users = c.fetchall()
        
        print("\n📊 GÜNCEL KULLANICI LİSTESİ:")
        print("-" * 40)
        for user in users:
            icon = "👑" if user[1] == "admin" else "👤"
            print(f"{icon} {user[0]} -> {user[1]}")
        
        print("\n🎉 Migration tamamlandı!")
        
    except Exception as e:
        print(f"❌ Hata: {e}")
        conn.rollback()
    
    finally:
        conn.close()

if __name__ == "__main__":
    migrate_database()
