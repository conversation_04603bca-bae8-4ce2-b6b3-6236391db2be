# Gmail Uygulama Şifresi Kurulumu

## 📧 Gmail Uygulama Şifresi Nasıl Alınır?

### 1. Google Hesap Ayarları
1. **Google Hesabınıza girin:** https://myaccount.google.com/
2. **Güvenlik** sekmesine tıklayın
3. **2 Adımlı Doğrulama** aktif o<PERSON> (eğer değilse aktif edin)

### 2. Uygulama Şifresi Oluşturma
1. **Uygulama şifreleri** bölümüne gidin: https://myaccount.google.com/apppasswords
2. **Uygulama seçin** dropdown'ından **"Mail"** seçin
3. **Cihaz seçin** dropdown'ından **"Diğer (özel ad)"** seçin
4. **"Şenkal Web Uygulaması"** yazın
5. **OLUŞTUR** butonuna tıklayın

### 3. Ş<PERSON><PERSON>i Kopyalama
1. **16 haneli şifreyi kopyalayın** (örnek: `abcd efgh ijkl mnop`)
2. Bu şifreyi `.env` dosyasındaki `MAIL_PASSWORD` kısmına yapıştırın
3. **Boşlukları kaldırın:** `abcdefghijklmnop`

### 4. .env Dosyasını Güncelleme
```env
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=abcdefghijklmnop
MAIL_DEFAULT_SENDER=<EMAIL>
```

## 🔧 Test Etme

### 1. Bağımlılıkları Yükle
```bash
pip install -r requirements.txt
```

### 2. Uygulamayı Çalıştır
```bash
python app.py
```

### 3. Şifre Sıfırlama Test Et
1. http://localhost:5000/sifremi-unuttum adresine git
2. Kayıtlı bir e-posta adresi gir
3. E-posta gelip gelmediğini kontrol et

## ⚠️ Önemli Notlar

### Güvenlik
- **Uygulama şifresini kimseyle paylaşmayın**
- **`.env` dosyasını git'e eklemeyin** (`.gitignore`'da zaten var)
- **Şifreyi düz metin olarak kodda yazmayın**

### Sorun Giderme
- **E-posta gelmiyorsa:** Spam klasörünü kontrol edin
- **Kimlik doğrulama hatası:** Uygulama şifresini yeniden oluşturun
- **2 Adımlı Doğrulama:** Mutlaka aktif olmalı

### Gmail Alternatifleri
Eğer Gmail kullanmak istemiyorsanız:
- **Outlook:** `smtp.outlook.com:587`
- **Yahoo:** `smtp.mail.yahoo.com:587`
- **Yandex:** `smtp.yandex.com:587`

## 📞 Destek
Sorun yaşarsanız:
- **E-posta:** <EMAIL>
- **Telefon:** 0530 179 95 58
