<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Şenkal Isıtma ve Soğutma</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .hamburger {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 10px;
        }

        .hamburger div {
            width: 25px;
            height: 3px;
            background-color: white;
            margin: 4px 0;
            transition: 0.4s;
        }

        .mobile-nav {
            display: none;
            flex-direction: column;
            background-color: #003366;
            text-align: center;
        }

        .mobile-nav a {
            padding: 10px;
            color: white;
            text-decoration: none;
            border-top: 1px solid #ffffff22;
        }

        @media (max-width: 768px) {
            nav {
                display: none;
            }

            .hamburger {
                display: flex;
            }

            .mobile-nav {
                display: none;
            }

            .mobile-nav.active {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <header>
        <h1>Şenkal Isıtma ve Soğutma Sistemleri</h1>

        <div class="hamburger" onclick="toggleMenu()">
            <div></div>
            <div></div>
            <div></div>
        </div>

        <nav>
            <a href="/">Anasayfa</a>
            <a href="/hakkimizda">Hakkımızda</a>
            <a href="/hizmetler">Hizmetler</a>
            <a href="/urunler">Ürünler</a>
            <a href="/iletisim">İletişim</a>
            {% if session.get("kullanici") %}
                <a href="/admin">Admin</a>
                <a href="/logout" style="color: #ff6b6b;">Çıkış</a>
            {% else %}
                <a href="/login">Giriş</a>
                <a href="/giris/google" style="background: #db4437; color: white; padding: 5px 10px; border-radius: 5px; text-decoration: none; margin-left: 10px;">
                    <i class="fab fa-google"></i> Google
                </a>
            {% endif %}
        </nav>

        <div class="mobile-nav" id="mobileNav">
            <a href="/">Anasayfa</a>
            <a href="/hakkimizda">Hakkımızda</a>
            <a href="/hizmetler">Hizmetler</a>
            <a href="/urunler">Ürünler</a>
            <a href="/iletisim">İletişim</a>
            {% if session.get("kullanici") %}
                <a href="/admin">Admin</a>
                <a href="/logout" style="color: #ff6b6b;">Çıkış ({{ session.kullanici }})</a>
            {% else %}
                <a href="/login">
                    <i class="fas fa-sign-in-alt"></i> Normal Giriş
                </a>
                <a href="/giris/google" style="background: #db4437; color: white;">
                    <i class="fab fa-google"></i> Google ile Giriş
                </a>
            {% endif %}
        </div>
    </header>
    <main>
        {% block content %}{% endblock %}
    </main>
    <footer>
        <p>© 2025 Şenkal Isıtma ve Soğutma</p>
    </footer>

    <script>
        function toggleMenu() {
            const mobileNav = document.getElementById("mobileNav");
            const hamburger = document.querySelector(".hamburger");

            mobileNav.classList.toggle("active");
            hamburger.classList.toggle("active");
        }

        // Menü dışına tıklandığında menüyü kapat
        document.addEventListener('click', function(event) {
            const mobileNav = document.getElementById("mobileNav");
            const hamburger = document.querySelector(".hamburger");
            const header = document.querySelector("header");

            if (!header.contains(event.target) && mobileNav.classList.contains("active")) {
                mobileNav.classList.remove("active");
                hamburger.classList.remove("active");
            }
        });
    </script>
</body>
</html>
