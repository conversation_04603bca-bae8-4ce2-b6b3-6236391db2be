{% extends "base.html" %}

{% block content %}
<div class="card">
    <h2><i class="fas fa-box"></i> Ürünlerimiz</h2>
    <p>Kaliteli beyaz eşya ve ısıtma-soğutma ürünleri</p>
</div>

<div class="card">
    <h3><i class="fas fa-filter"></i> Kategori Filtresi</h3>
    <form method="GET" action="/urunler">
        <label><i class="fas fa-tags"></i> Kategori:</label>
        <select name="kategori" onchange="this.form.submit()" style="padding: 8px; border-radius: 5px; border: 1px solid #ddd; width: 200px;">
            <option value="">Tüm <PERSON></option>
            {% for kategori in kategoriler %}
                <option value="{{ kategori[0] }}" {% if secili_kategori == kategori[0] %}selected{% endif %}>{{ kategori[0] }}</option>
            {% endfor %}
        </select>
    </form>
</div>

{% if urunler %}
<div style="display: flex; flex-wrap: wrap; gap: 20px; justify-content: center;">
    {% for urun in urunler %}
    <div style="width: 280px; background: white; border-radius: 15px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); overflow: hidden; transition: transform 0.3s ease;">
        {% if urun[4] %}
        <img src="{{ urun[4] }}" alt="{{ urun[1] }}" style="width: 100%; height: 200px; object-fit: cover;">
        {% else %}
        <img src="https://via.placeholder.com/280x200?text=Ürün+Resmi" alt="{{ urun[1] }}" style="width: 100%; height: 200px; object-fit: cover;">
        {% endif %}
        <div style="padding: 20px;">
            <h3 style="margin: 0 0 10px 0; color: #003366;"><i class="fas fa-tag"></i> {{ urun[1] }}</h3>
            <p style="color: #666; margin: 10px 0; min-height: 40px;">{{ urun[2] if urun[2] else 'Detaylı bilgi için arayınız.' }}</p>
            {% if urun[5] %}
            <div style="background: #17a2b8; color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; display: inline-block; margin-bottom: 10px;">
                <i class="fas fa-tags"></i> {{ urun[5] }}
            </div>
            {% endif %}
            {% if urun[3] %}
            <div style="font-size: 1.5rem; font-weight: bold; color: #0077cc; margin: 15px 0;">{{ "%.2f"|format(urun[3]) }} TL</div>
            {% endif %}
            <small style="color: #999;">Ürün Kodu: #{{ urun[0] }}</small>
            <div style="margin-top: 15px;">
                <button style="background: #0077cc; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; width: 100%;">
                    <i class="fas fa-phone"></i> Bilgi Al
                </button>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="card" style="text-align: center; background: #f8f9fa;">
    <i class="fas fa-info-circle" style="font-size: 3rem; color: #0077cc; margin-bottom: 15px;"></i>
    <h3>Henüz ürün eklenmemiş</h3>
    <p>Yakında satışa sunacağımız ürünler burada listelenecektir.</p>
    <p>Ürün bilgileri için lütfen bizimle iletişime geçin.</p>
</div>
{% endif %}

<div class="card" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; text-align: center;">
    <h3><i class="fas fa-phone-alt"></i> Ürün Bilgisi ve Fiyat İçin</h3>
    <p style="font-size: 1.1rem;">Detaylı bilgi almak için bize ulaşın</p>
    <p style="font-size: 1.8rem; font-weight: bold;">📞 0555 123 45 67</p>
</div>
{% endblock %}
